/* 加载状态动画 */
.loading-pulse {
    position: relative;
    opacity: 0.7;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-fade {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.error-state {
    color: #dc3545;
    transition: color 0.3s ease;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 0.4; }
    100% { opacity: 0.7; }
}

/* 确保加载状态时文本可读 */
.loading-pulse, .loading-fade {
    cursor: wait;
}

/* 调整 Font Awesome fa-spin 动画速度 */
.fa-spin {
  -webkit-animation: fa-spin 1s infinite linear;
  animation: fa-spin 1s infinite linear;
}

/* 如果需要更细致的控制，可以覆盖 @keyframes 规则，但通常调整 animation 属性就足够了 */
/* 
@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
*/

/* 在HTML的<style>标签中添加这个样式 */
.email-item.active {
    background-color: #e5e7eb;
    border-left: 3px solid #3b82f6;
}
