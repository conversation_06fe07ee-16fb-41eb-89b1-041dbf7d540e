<!DOCTYPE html>
<html>
<head>
    <title>测试自定义前缀功能</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
        #result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>自定义邮箱前缀功能测试</h1>
    
    <div class="test-section">
        <h3>1. 测试自定义前缀API</h3>
        <input type="text" id="prefixInput" placeholder="输入自定义前缀" value="mytest">
        <button onclick="testCustomPrefix()">测试自定义前缀</button>
        <button onclick="testDefaultPrefix()">测试默认前缀</button>
        <div id="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 前端按钮测试</h3>
        <p class="info">请打开浏览器开发者工具，然后访问主页面检查Custom按钮是否存在</p>
        <button onclick="window.open('/', '_blank')">打开主页面</button>
    </div>

    <script>
        async function testCustomPrefix() {
            const prefix = document.getElementById('prefixInput').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('/api/generate-address', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ custom_prefix: prefix })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 成功！生成邮箱: ${result.data.address}</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 失败: ${result.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }
        
        async function testDefaultPrefix() {
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('/api/generate-address', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 成功！生成邮箱: ${result.data.address}</span>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 失败: ${result.error}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
