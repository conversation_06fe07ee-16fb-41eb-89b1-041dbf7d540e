import sqlite3
import datetime
import os
import sys
import logging
from dotenv import load_dotenv

# 获取脚本所在目录，并加载该目录下的 .env 文件
script_dir = os.path.dirname(os.path.abspath(__file__))
dotenv_path = os.path.join(script_dir, '.env')
load_dotenv(dotenv_path=dotenv_path)

DATABASE_PATH = os.getenv('DATABASE_PATH')
LOG_FILE_CLEANUP = os.getenv('LOG_FILE_CLEANUP', os.path.join(script_dir, 'logs/cleanup.log'))

# 日志配置
if LOG_FILE_CLEANUP:
    log_dir = os.path.dirname(LOG_FILE_CLEANUP)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    logging.basicConfig(filename=LOG_FILE_CLEANUP, level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s')
else:
    logging.basicConfig(level=logging.INFO, stream=sys.stderr,
                        format='%(asctime)s - %(levelname)s - %(message)s')

def cleanup_expired_emails(database_path=None):
    """清理已过期的临时邮箱地址及其关联邮件"""
    db_path = database_path if database_path is not None else os.getenv('DATABASE_PATH')
    if not db_path:
        logging.error("DATABASE_PATH 未配置。清理中止。")
        return

    conn = None
    try:
        # 确保数据库目录存在 (虽然通常由 app.py 或 mail_handler.py 创建)
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 启用外键约束（SQLite默认关闭）
        cursor.execute("PRAGMA foreign_keys = ON")

        # 使用 UTC 时间，避免时区问题
        now_iso = datetime.datetime.now(datetime.UTC).isoformat()

        # 获取将要删除的邮箱地址 (用于日志记录)
        cursor.execute("SELECT id, address FROM temporary_emails WHERE expires_at < ?", (now_iso,))
        expired_emails_info = cursor.fetchall()

        if not expired_emails_info:
            logging.info("没有找到需要清理的过期邮箱地址。")
            return

        for record in expired_emails_info:
            # sqlite3 默认返回 tuple，不支持 dict 访问
            logging.info("准备删除过期邮箱: %s (ID: %s)", record[1], record[0])

        # 删除过期的 temporary_emails 记录
        # ON DELETE CASCADE 会自动删除 received_mails 表中关联的邮件
        cursor.execute("DELETE FROM temporary_emails WHERE expires_at < ?", (now_iso,))
        deleted_count = cursor.rowcount # 获取实际删除的行数

        conn.commit()
        logging.info("清理完成。共删除了 %d 个过期邮箱地址。", deleted_count)

    except sqlite3.Error as e:
        logging.error("清理过程中发生数据库错误: %s", e)
        if conn:
            conn.rollback() # 回滚事务
    except Exception as e:
        logging.error("清理过程中发生未捕获的异常: %s", e, exc_info=True)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    logging.info("清理脚本开始执行。")
    cleanup_expired_emails()
    logging.info("清理脚本执行完毕。")