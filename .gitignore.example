# Python bytecode and compiled files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg
*.whl
MANIFEST

# Virtual Environments
venv/
.venv/
env/
ENV/
pip-freeze.txt # 通常 requirements.txt 会被提交，但 pip-freeze.txt 可能是临时的

# SQLite database files
*.db
*.sqlite
*.sqlite3
# 具体指定您的数据库文件（如果它在特定位置且名称固定）
# 假设您的数据库文件名为 temp_mail_mvp.db 且在 database/ 目录下
/var/www/tempmail_project/database/tempmail.db
# 如果您有一个初始的空数据库结构文件想要提交，可以使用否定模式，例如:
# !database/schema_init.db

# Log files
*.log
logs/               # Flask 应用的日志目录 (例如 app.log)
mail_handler_logs/  # 您邮件处理脚本的日志目录 (如果按之前建议创建了)
gunicorn_*.log      # Gunicorn 可能产生的日志文件

# Runtime files and directories
run/                # 用于存放 Gunicorn socket 等运行时文件

# Environment variables / sensitive configuration
.env
*.env
config.ini          # 如果包含敏感信息
secrets.json        # 如果包含敏感信息

# IDE / Editor specific files
.vscode/
.idea/
*.project
*.tmproj
*.sublime-workspace
*.swp
*.swo
*~

# OS-generated files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Certbot (Let's Encrypt) - 通常不在项目目录，但以防万一
.well-known/

# Coverage reports
.coverage
coverage.xml
htmlcov/

# Other temporary files
tmp/
temp/
