import sys
import os
import sqlite3
import datetime
import logging
from email import message_from_bytes
from email.header import decode_header
from dotenv import load_dotenv

# --- Exit Code Constants (from sysexits.h) ---
EX_OK = 0           # successful termination
EX_USAGE = 64       # command line usage error
EX_DATAERR = 65     # data format error
EX_NOUSER = 67      # addressee unknown
EX_UNAVAILABLE = 69 # service unavailable (e.g., required resource missing, or permanent DB issue)
EX_TEMPFAIL = 75    # temp failure; user is invited to retry
EX_CONFIG = 78      # configuration error

# 获取脚本所在目录，并加载该目录下的 .env 文件
script_dir = os.path.dirname(os.path.abspath(__file__))
dotenv_path = os.path.join(script_dir, '.env')
load_dotenv(dotenv_path=dotenv_path)

DATABASE_PATH = os.getenv('DATABASE_PATH')
LOG_FILE_MAIL_HANDLER = os.getenv('LOG_FILE_MAIL_HANDLER', os.path.join(script_dir, 'logs/mail_handler.log'))

# 日志配置
if LOG_FILE_MAIL_HANDLER:
    log_dir = os.path.dirname(LOG_FILE_MAIL_HANDLER)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True) # exist_ok=True 避免并发问题
        except OSError as e:
            # 如果无法创建日志目录，这将是一个严重的问题，但脚本可能仍需尝试运行或以特定错误退出
            # 暂时保持简单，如果失败，basicConfig 会回退到 stderr
            print(f"警告: 无法创建 mail_handler 的日志目录 {log_dir}: {e}")
    logging.basicConfig(filename=LOG_FILE_MAIL_HANDLER, level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(process)d - %(message)s')
else: # 如果没有配置日志文件，则输出到标准错误流
    logging.basicConfig(level=logging.INFO, stream=sys.stderr,
                        format='%(asctime)s - %(levelname)s - %(process)d - %(message)s')


def decode_email_header(header_value):
    """解码邮件头字段 (如 Subject, From)"""
    if not header_value:
        return ""
    decoded_parts = []
    for part_bytes, charset in decode_header(header_value):
        if isinstance(part_bytes, bytes):
            try:
                decoded_parts.append(part_bytes.decode(charset or 'utf-8', errors='replace'))
            except LookupError: # 未知编码
                decoded_parts.append(part_bytes.decode('utf-8', errors='replace')) # 尝试UTF-8
        else: # 已经是解码后的字符串
            decoded_parts.append(part_bytes)
    return "".join(decoded_parts)

def parse_email_body(msg):
    """从 email.message 对象中提取纯文本正文，优化处理"""
    body_text = ""
    body_html = None  # 仅作存档，不处理
    
    def clean_text(text):
        """清理和标准化纯文本内容"""
        if not text:
            return ""
        # 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        # 移除多余的空行
        text = '\n'.join(line for line in text.splitlines() if line.strip())
        # 确保UTF-8兼容
        return text.encode('utf-8', errors='replace').decode('utf-8')

    if msg.is_multipart():
        for part in msg.walk():
            ctype = part.get_content_type()
            cdispo = str(part.get('Content-Disposition'))
            
            if 'attachment' in cdispo.lower():
                continue
                
            try:
                payload = part.get_payload(decode=True)
                if not payload:
                    continue
                    
                charset = part.get_content_charset() or 'utf-8'
                if ctype == 'text/plain' and not body_text:
                    decoded = payload.decode(charset, errors='replace')
                    body_text = clean_text(decoded)
                elif ctype == 'text/html' and not body_html:
                    # 仅存储HTML，不处理
                    body_html = payload.decode(charset, errors='replace')
            except Exception as e:
                logging.warning(f"解析邮件部分时出错 (type: {ctype}): {e}")
    else:
        try:
            payload = msg.get_payload(decode=True)
            if payload:
                charset = msg.get_content_charset() or 'utf-8'
                ctype = msg.get_content_type()
                if ctype == 'text/plain':
                    decoded = payload.decode(charset, errors='replace')
                    body_text = clean_text(decoded)
                elif ctype == 'text/html':
                    body_html = payload.decode(charset, errors='replace')
        except Exception as e:
            logging.warning(f"解析单部分邮件时出错 (type: {msg.get_content_type()}): {e}")
    
    # 如果没有找到纯文本内容，记录警告
    if not body_text:
        logging.warning("未找到纯文本内容，邮件可能为纯HTML格式")
        body_text = "（该邮件没有纯文本内容）"
    
    return body_text, body_html

def store_email(recipient_address, sender, subject, body_text, body_html):
    """将解析后的邮件存储到数据库"""
    if not DATABASE_PATH:
        logging.error("DATABASE_PATH 未配置，无法存储邮件。")
        return EX_CONFIG

    conn = None
    success = EX_TEMPFAIL  # 默认用 EX_TEMPFAIL 作为失败返回码
    try:
        db_dir = os.path.dirname(DATABASE_PATH)
        if db_dir and not os.path.exists(db_dir):
            try:
                os.makedirs(db_dir, exist_ok=True)
            except OSError as e:
                logging.error(f"无法创建数据库目录 {db_dir}: {e}")
                return EX_CONFIG

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute("SELECT id, expires_at FROM temporary_emails WHERE address = ?", (recipient_address,))
        email_record = cursor.fetchone()

        if not email_record:
            logging.warning(f"收件地址 {recipient_address} 在 temporary_emails 中未找到。丢弃邮件。")
            return EX_NOUSER

        email_id, expires_at_str = email_record[0], email_record[1]
        try:
            expires_at_dt = datetime.datetime.fromisoformat(expires_at_str)
            if expires_at_dt.tzinfo is None or expires_at_dt.tzinfo.utcoffset(expires_at_dt) is None:
                expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)
        except ValueError as e:
            logging.error(f"无法解析邮箱 {recipient_address} 的过期时间 '{expires_at_str}': {e}")
            return EX_DATAERR

        now_utc = datetime.datetime.now(datetime.timezone.utc)
        if now_utc > expires_at_dt:
            logging.warning(f"收件地址 {recipient_address} 已过期 ({expires_at_str})。丢弃邮件。")
            return EX_NOUSER

        current_time_iso = now_utc.isoformat()
        cursor.execute(
            """INSERT INTO received_mails (email_address_id, sender, subject, body_text, body_html, received_at)
               VALUES (?, ?, ?, ?, ?, ?)""",
            (email_id, sender, subject, body_text, body_html, current_time_iso)
        )
        conn.commit()
        logging.info(f"成功为 {recipient_address} 存储来自 {sender} 的邮件，主题: '{subject[:50]}...'")
        success = EX_OK

    except sqlite3.OperationalError as e:
        logging.error(f"为 {recipient_address} 存储邮件时发生数据库操作错误 (可能临时): {e}", exc_info=True)
        if conn:
            conn.rollback()
        success = EX_TEMPFAIL
    except sqlite3.IntegrityError as e:
        logging.error(f"为 {recipient_address} 存储邮件时发生数据库完整性错误 (通常是永久性，除非是重复投递): {e}", exc_info=True)
        if conn:
            conn.rollback()
        success = EX_DATAERR
    except sqlite3.Error as e:
        logging.error(f"为 {recipient_address} 存储邮件时发生未知数据库错误: {e}", exc_info=True)
        if conn:
            conn.rollback()
        success = EX_TEMPFAIL
    except Exception as e:
        logging.error(f"为 {recipient_address} 存储邮件时发生未知程序错误: {e}", exc_info=True)
        if conn:
            conn.rollback()
        success = EX_TEMPFAIL
    finally:
        if conn:
            conn.close()
        return success  # 这里始终返回退出码常量
            
def main():
    # 检查 DATABASE_PATH 是否在模块加载时已正确设置
    if not DATABASE_PATH:
        # 这个日志在 basicConfig 初始化前可能不会写入文件，但会尝试 stderr
        logging.critical("严重启动错误: 脚本关键配置 DATABASE_PATH 未在环境变量中设置。")
        sys.exit(EX_CONFIG) # 脚本无法在无数据库路径配置下运行

    if len(sys.argv) < 2:
        logging.error("错误: 未提供收件人地址作为命令行参数。")
        sys.exit(EX_USAGE)  # 放在这里

    recipient = sys.argv[1]
    logging.info(f"邮件处理器被调用，收件人: {recipient}")

    exit_code = EX_TEMPFAIL # 为 exit_code 设置一个默认的失败值

    try:
        raw_email_bytes = sys.stdin.buffer.read()
        if not raw_email_bytes:
            logging.warning("从 stdin 读取的邮件内容为空，可能发生错误。")
            exit_code = EX_DATAERR # 设置正确的退出码
        else:
            # 只有在有邮件内容时才继续处理
            msg = message_from_bytes(raw_email_bytes)
            sender = decode_email_header(msg.get('From', 'Unknown Sender'))
            subject = decode_email_header(msg.get('Subject', 'No Subject'))
            
            # parse_email_body 也可能抛出异常，会被下面的 except 块捕获
            body_text, body_html = parse_email_body(msg)
            
            # store_email 函数现在应该返回一个 EX_... 状态码
            exit_code = store_email(recipient, sender, subject, body_text, body_html)
            
    except sqlite3.Error as db_err: 
        logging.error(f"mail_handler.py 数据库错误 (收件人: {recipient}): {db_err}", exc_info=True)
        exit_code = EX_TEMPFAIL # 数据库相关错误通常视为临时
    except (TypeError, ValueError, AttributeError) as data_handling_err: 
        # 这些通常是邮件解析或数据处理中发生的、可能是永久性的数据格式错误
        logging.error(f"mail_handler.py 数据处理/解析错误 (收件人: {recipient}): {data_handling_err}", exc_info=True)
        exit_code = EX_DATAERR
    except Exception as e: 
        # 其他所有未预料到的异常
        logging.error(f"mail_handler.py 未预料的主流程异常 (收件人: {recipient}): {e}", exc_info=True)
        exit_code = EX_TEMPFAIL # 默认为临时失败，以便 Postfix 重试
    finally:
        # 这是唯一的退出点，日志中记录的 exit_code 就是脚本将要使用的退出码
        logging.info(f"邮件处理完毕 (收件人: {recipient})，脚本最终退出码: {exit_code}")
        sys.exit(exit_code)  # 使用正确的退出码

if __name__ == "__main__":
    main()