# API配置环境变量示例
# 复制此文件为 .env.api 并根据需要修改

#===================================
# API基础配置
#===================================
# API基础URL（为空则使用相对路径）
API_BASE_URL=

# API请求超时时间（毫秒）
API_TIMEOUT=10000

# API重试次数
API_RETRY_ATTEMPTS=3

# API重试延迟（毫秒）
API_RETRY_DELAY=1000

#===================================
# 开发环境示例配置
#===================================
# 开发环境可以使用完整URL指向不同服务器
# API_BASE_URL=http://localhost:5001
# API_BASE_URL=http://*************:5000
# API_BASE_URL=https://api-dev.tempmail.com

#===================================
# 生产环境示例配置
#===================================
# 生产环境通常使用相对路径（留空）或完整域名
# API_BASE_URL=
# API_BASE_URL=https://api.tempmail.com
# API_TIMEOUT=15000
# API_RETRY_ATTEMPTS=5

#===================================
# 测试环境示例配置
#===================================
# 测试环境可能需要特定的API配置
# API_BASE_URL=http://test-api.local:8080
# API_TIMEOUT=5000
# API_RETRY_ATTEMPTS=1
